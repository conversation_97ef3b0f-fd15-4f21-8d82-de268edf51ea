<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Previews - GigGenius</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .preview-card {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .preview-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        .preview-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .preview-card p {
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .btn.admin {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
        }
        .note {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .note p {
            margin: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Template Previews</h1>

        <div class="note">
            <p><strong>💡 Tip:</strong> Use these previews to see how your emails will look before making changes. No need to create accounts!</p>
        </div>

        <div class="preview-grid">
            <div class="preview-card">
                <h3>👨‍💻 Genius Welcome Email</h3>
                <p>See how the welcome email looks for genius registrations</p>
                <a href="/preview_genius_welcome" class="btn" target="_blank">Preview Genius Email</a>
            </div>

            <div class="preview-card">
                <h3>🏢 Client Welcome Email</h3>
                <p>See how the welcome email looks for client registrations</p>
                <a href="/preview_client_welcome" class="btn" target="_blank">Preview Client Email</a>
            </div>

            <div class="preview-card">
                <h3>🔔 Admin Genius Notification</h3>
                <p>See how admin notifications look for genius registrations</p>
                <a href="/preview_admin_genius" class="btn admin" target="_blank">Preview Admin Email</a>
            </div>

            <div class="preview-card">
                <h3>🔔 Admin Client Notification</h3>
                <p>See how admin notifications look for client registrations</p>
                <a href="/preview_admin_client" class="btn admin" target="_blank">Preview Admin Email</a>
            </div>

            <div class="preview-card">
                <h3>✅ Genius Approval Email</h3>
                <p>See how approval emails look for genius accounts</p>
                <a href="/preview_genius_approval" class="btn" style="background: linear-gradient(135deg, #22c55e, #16a34a);" target="_blank">Preview Approval Email</a>
            </div>

            <div class="preview-card">
                <h3>❌ Genius Decline Email</h3>
                <p>See how decline notification emails look for genius registrations</p>
                <a href="/preview_genius_decline" class="btn" style="background: linear-gradient(135deg, #dc3545, #c82333);" target="_blank">Preview Decline Email</a>
            </div>

            <div class="preview-card">
                <h3>✅ Client Approval Email</h3>
                <p>See how approval emails look for client accounts</p>
                <a href="/preview_client_approval" class="btn" style="background: linear-gradient(135deg, #22c55e, #16a34a);" target="_blank">Preview Approval Email</a>
            </div>

            <div class="preview-card">
                <h3>❌ Client Decline Email</h3>
                <p>See how decline notification emails look for client registrations</p>
                <a href="/preview_client_decline" class="btn" style="background: linear-gradient(135deg, #dc3545, #c82333);" target="_blank">Preview Decline Email</a>
            </div>

            <div class="preview-card">
                <h3>🎉 Job Posting Confirmation</h3>
                <p>See how job posting confirmation emails look for clients</p>
                <a href="/preview_job_posting_confirmation_public" class="btn" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);" target="_blank">Preview Job Confirmation</a>
            </div>

            <div class="preview-card">
                <h3>✅ Proposal Submitted Confirmation</h3>
                <p>See how proposal submission confirmation emails look for geniuses</p>
                <a href="/preview_proposal_submitted_confirmation" class="btn" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);" target="_blank">Preview Proposal Confirmation</a>
            </div>

            <div class="preview-card">
                <h3>🎯 New Applicant Notification</h3>
                <p>See how new applicant notification emails look for clients</p>
                <a href="/preview_new_applicant_notification" class="btn" style="background: linear-gradient(135deg, #10b981, #059669);" target="_blank">Preview New Applicant</a>
            </div>

            <div class="preview-card">
                <h3>🎉 Job Offer Accepted</h3>
                <p>See how job offer acceptance notification emails look for clients</p>
                <a href="/preview_job_offer_accepted" class="btn" style="background: linear-gradient(135deg, #22c55e, #16a34a);" target="_blank">Preview Offer Accepted</a>
            </div>

            <div class="preview-card">
                <h3>😔 Application Not Selected</h3>
                <p>See how application rejection emails look for geniuses</p>
                <a href="/preview_application_not_selected" class="btn" style="background: linear-gradient(135deg, #f59e0b, #d97706);" target="_blank">Preview Not Selected</a>
            </div>

            <div class="preview-card">
                <h3>🎉 Application Accepted</h3>
                <p>See how application acceptance emails look for geniuses</p>
                <a href="/preview_application_accepted" class="btn" style="background: linear-gradient(135deg, #22c55e, #16a34a);" target="_blank">Preview Accepted</a>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="color: #667eea; text-decoration: none;">← Back to Home</a>
        </div>
    </div>
</body>
</html>
